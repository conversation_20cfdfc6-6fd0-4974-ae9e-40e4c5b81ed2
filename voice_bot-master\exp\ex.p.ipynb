{"cells": [{"cell_type": "code", "execution_count": 43, "id": "3af21f01", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "from datetime import datetime\n", "from dotenv import load_dotenv\n", "from typing import List, Dict, Any\n", "from langchain.schema import Document\n", "from langchain_community.utilities import GoogleSerperAPIWrapper\n", "\n", "from src.constants.agent_constants import GraphState\n", "from src.utils.exception import CustomException\n", "from src.utils.logger import logging\n", "from src.utils.main_utils import measure_time\n", "\n", "load_dotenv()\n", "\n", "class GoogleSearchTool:\n", "    def __init__(self) -> None:\n", "        self.search_client = GoogleSerperAPIWrapper(\n", "            hl='en',\n", "            k=5,\n", "            serper_api_key=os.getenv('GOOGLE_SEARCH_API')\n", "        )\n", "\n", "    def search(self, query: str) -> List[Document]:\n", "        try:\n", "            # Optional: add date filtering here if needed\n", "            results = self.search_client.results(query)\n", "\n", "            documents = []\n", "            for item in results.get(\"organic\", []):\n", "                link = item.get(\"link\", \"\")\n", "                snippet = item.get(\"snippet\", \"\")\n", "\n", "                # Keep only non-empty snippets\n", "                if len(snippet.strip()) > 10:\n", "                    documents.append(snippet)\n", "\n", "            logging.info(f\"Search returned {len(documents)} documents\")\n", "            return \",\".join(documents)\n", "\n", "        except Exception as e:\n", "            logging.error(f\"Search error: {e}\")\n", "            raise CustomException(e, sys)\n", "\n", "    @measure_time\n", "    def web_search(self, state: GraphState) -> Dict[str, Any]:\n", "        logging.info(\"======================= Web search tool invoked =======================\")\n", "        question = state['question']\n", "\n", "        try:\n", "            response= self.search(question)\n", "            print(response)\n", "            documents = response\n", "            \n", "            return {\"documents\": documents, \"question\": question}\n", "\n", "        except Exception as e:\n", "            logging.error(f\"Web search failed: {e}\")\n", "            raise CustomException(e, sys)\n"]}, {"cell_type": "code", "execution_count": 44, "id": "471b5265", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON> is the 47th and current president since January 20, 2025. Contents. 1 History and development. 1.1 Origins; 1.2 1789–1933; 1.3 Imperial ...,After a landslide election victory in 2024, President <PERSON> is returning to the White House to build upon his previous successes and use his mandate ...,<PERSON>. President of the United States · J<PERSON> <PERSON>. VICE PRESIDENT OF THE UNITED STATES · <PERSON><PERSON>. First Lady OF THE UNITED STATES · The Cabinet. Of ...,President <PERSON> 45th & 47th President of the United States. The Golden Age of America Begins Right Now.,The 47th and current president of the United States is <PERSON>. He was sworn into office on January 20, 2025.\n"]}], "source": ["obj= GoogleSearchTool()\n", "response=obj.web_search(\n", "\t{\n", "\t\t\"question\":  \"Who is the current prasident of USA?\"\n", "\t}\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b639e7fc", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(metadata={'title': 'President of the United States - Wikipedia', 'link': 'https://en.wikipedia.org/wiki/President_of_the_United_States', 'query': 'Who is the current prasident of USA?'}, page_content='<PERSON> is the 47th and current president since January 20, 2025. Contents. 1 History and development. 1.1 Origins; 1.2 1789–1933; 1.3 Imperial ...'),\n", " Document(metadata={'title': 'The Trump Administration - The White House', 'link': 'https://www.whitehouse.gov/administration/', 'query': 'Who is the current prasident of USA?'}, page_content='After a landslide election victory in 2024, President <PERSON> is returning to the White House to build upon his previous successes and use his mandate ...'),\n", " Document(metadata={'title': 'The White House', 'link': 'https://www.whitehouse.gov/', 'query': 'Who is the current prasident of USA?'}, page_content='<PERSON>. President of the United States · J<PERSON> <PERSON>. VICE PRESIDENT OF THE UNITED STATES · <PERSON><PERSON>. First Lady OF THE UNITED STATES · The Cabinet. Of ...'),\n", " Document(metadata={'title': 'President <PERSON> (@potus) • Instagram photos and videos', 'link': 'https://www.instagram.com/potus/?hl=en', 'query': 'Who is the current prasident of USA?'}, page_content='President <PERSON> 45th & 47th President of the United States. The Golden Age of America Begins Right Now.'),\n", " Document(metadata={'title': 'Presidents, vice presidents, and first ladies | USAGov', 'link': 'https://www.usa.gov/presidents', 'query': 'Who is the current prasident of USA?'}, page_content='The 47th and current president of the United States is <PERSON>. He was sworn into office on January 20, 2025.')]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["response['documents']"]}, {"cell_type": "code", "execution_count": 16, "id": "9eeb1863", "metadata": {}, "outputs": [{"data": {"text/plain": ["'<PERSON>. President of the United States · <PERSON><PERSON> <PERSON>. VICE PRESIDENT OF THE UNITED STATES · <PERSON><PERSON>. First Lady OF THE UNITED STATES · The Cabinet. Of ...'"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["response['documents'][1].page_content"]}, {"cell_type": "code", "execution_count": 73, "id": "f08d4c70", "metadata": {}, "outputs": [{"data": {"text/plain": ["Document(metadata={'title': 'The White House', 'link': 'https://www.whitehouse.gov/', 'query': 'Who is the current prasident of USA?'}, page_content='<PERSON>. President of the United States · J<PERSON> <PERSON>. VICE PRESIDENT OF THE UNITED STATES · <PERSON><PERSON>. First Lady OF THE UNITED STATES · The Cabinet. Of ...')"]}, "execution_count": 73, "metadata": {}, "output_type": "execute_result"}], "source": ["response['documents'][1]"]}, {"cell_type": "code", "execution_count": 61, "id": "2eedb412", "metadata": {}, "outputs": [{"data": {"text/plain": ["'The following is a list of events of the year 2025 in the United States, as well as predicted and scheduled events that have not yet occurred.'"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["response['documents'][3].page_content"]}, {"cell_type": "code", "execution_count": 17, "id": "c90e8860", "metadata": {}, "outputs": [], "source": ["data = []\n", "for i in range(len(response[\"documents\"])):\n", "    content = response[\"documents\"][i].page_content\n", "    if len(content) > 10:\n", "        data.append(content)\n"]}, {"cell_type": "code", "execution_count": 18, "id": "d19d85b0", "metadata": {}, "outputs": [], "source": ["data = \" \".join(\n", "    response[\"documents\"][i].page_content\n", "    for i in range(len(response[\"documents\"]))\n", "    if len(response[\"documents\"][i].page_content) > 10\n", ")\n"]}, {"cell_type": "code", "execution_count": 19, "id": "9d5820c6", "metadata": {}, "outputs": [{"data": {"text/plain": ["'<PERSON> is the 47th and current president since January 20, 2025. Contents. 1 History and development. 1.1 Origins; 1.2 1789–1933; 1.3 Imperial .<PERSON>. <PERSON>. President of the United States · J<PERSON> <PERSON>. VICE PRESIDENT OF THE UNITED STATES · <PERSON><PERSON>. First Lady OF THE UNITED STATES · The Cabinet. Of ... <PERSON>. 45th & 47th President of the United States. After a landslide election victory in 2024, President <PERSON> is returning to the White ... President <PERSON> 45th & 47th President of the United States. The Golden Age of America Begins Right Now. The 47th and current president of the United States is <PERSON>. He was sworn into office on January 20, 2025.'"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}, {"cell_type": "code", "execution_count": null, "id": "3b86a387", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 5}