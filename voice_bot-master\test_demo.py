#!/usr/bin/env python3
"""
Test script to verify the demo.py implementation
"""
import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all imports work correctly"""
    try:
        from demo import LanguageSwitcherAgent, create_tool_function
        print("✅ Successfully imported LanguageSwitcherAgent and create_tool_function")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_agent_creation():
    """Test that the agent can be created"""
    try:
        from demo import LanguageSwitcherAgent
        
        # Mock environment variables for testing
        os.environ.setdefault("DEEPGRAM_API_KEY", "test_key")
        os.environ.setdefault("GROQ_API_KEY", "test_key")
        
        agent = LanguageSwitcherAgent()
        print("✅ Successfully created LanguageSwitcherAgent")
        
        # Test language mappings
        assert "en" in agent.language_names
        assert "es" in agent.language_names
        assert "fr" in agent.language_names
        assert "de" in agent.language_names
        assert "it" in agent.language_names
        print("✅ Language mappings are correct")
        
        # Test that ElevenLabs is configured
        assert agent.tts is not None
        print("✅ ElevenLabs TTS is configured")
        
        # Test that Deepgram STT is configured
        assert agent.stt is not None
        print("✅ Deepgram STT is configured")
        
        return True
    except Exception as e:
        print(f"❌ Agent creation error: {e}")
        return False

def test_language_codes():
    """Test that language codes are properly mapped"""
    try:
        from demo import LanguageSwitcherAgent
        
        os.environ.setdefault("DEEPGRAM_API_KEY", "test_key")
        os.environ.setdefault("GROQ_API_KEY", "test_key")
        
        agent = LanguageSwitcherAgent()
        
        # Test ElevenLabs language codes
        expected_elevenlabs = {"en": "en", "es": "es", "fr": "fr", "de": "de", "it": "it"}
        assert agent.elevenlabs_language_codes == expected_elevenlabs
        print("✅ ElevenLabs language codes are correct")
        
        # Test Deepgram language codes
        expected_deepgram = {"en": "en", "es": "es", "fr": "fr-CA", "de": "de", "it": "it"}
        assert agent.deepgram_language_codes == expected_deepgram
        print("✅ Deepgram language codes are correct")
        
        return True
    except Exception as e:
        print(f"❌ Language codes test error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing demo.py implementation...")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_agent_creation,
        test_language_codes
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print(f"\n🔍 Running {test.__name__}...")
        if test():
            passed += 1
        else:
            print(f"❌ {test.__name__} failed")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The implementation looks good.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
