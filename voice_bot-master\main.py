from src.pipeline.agentic_rag_pipeline import AgenticRAG

obj = AgenticRAG()


questions ={
    # "What is the Langgraph Agent",
    "What is the todays tremarature of Hydrabard",
    # "What is Self-rag",
    # "what is vectore database",
    # # "Can you tell me about nkt cables Joint ?",
    # # "what are the specifictions of abb joint smpgb premolded",
    # "Who is the Current Cm of Andra Pradash",
    # "What are the key components of a abb switchgear system?",
    # "Who is the current prasident of USA?"

}
print("="*15)
print("Start the Session")
for q in questions:
    response = obj.run(q)
    print("*"*15)
    print(f"question:{q}")
    print(f"Response: {response['generation']}")
    print("-"*15)

