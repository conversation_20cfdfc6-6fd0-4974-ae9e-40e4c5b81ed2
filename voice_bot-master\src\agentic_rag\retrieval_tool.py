import sys
from concurrent.futures import ThreadPoolExecutor
import threading

from langchain_core.prompts import ChatPromptTemplate
from langchain.chains import create_retrieval_chain
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain_community.vectorstores import FAISS
from langchain import llms
from langchain_huggingface import HuggingFaceEmbeddings
from langchain import llms

from src.utils.logger import logging
from src.utils.exception import CustomException
from src.prompt import system_prompt
from src.utils.main_utils import measure_time, read_yaml
from src.constants import config_file_path
from src.constants.agent_constants import GraphState


class RetrieverTool:
    def __init__(self,llm,embeddings:HuggingFaceEmbeddings, max_workers: int = 6) -> None:
        configuration = read_yaml(config_file_path)
        self.config = configuration['RAG']
        self.vectore_store_path = self.config['vectore_db_path']
        self.embeddings=embeddings

        self.max_workers = max_workers
        self.llm = llm

        # Simple thread lock for safety
        self.lock = threading.Lock()
        
        # Load components in parallel (faster startup)
        self._parallel_init()

        logging.info("Embedding Load Successfully")
		# Load retriever
        self.retriever, self.vectore_store=self._load_retriever()
        
        # Create the RAG chain
        self._create_chain()

    @measure_time
    def _parallel_init(self):
        """Load LLM and retriever at the same time"""
        print("Loading components...")
        
        with ThreadPoolExecutor(max_workers=4) as executor:
            # Start loading  components simultaneously
            retriever_future = executor.submit(self._load_retriever)
            
            # Wait for  complete
            self.retriever = retriever_future.result()
            
        print("Components loaded!")
    
    def _load_retriever(self):
        """Load the vector database"""

        vector_db = FAISS.load_local(
            folder_path=self.vectore_store_path,
            embeddings=self.embeddings,
            allow_dangerous_deserialization=True
        )

        vectordb_retriver = vector_db.as_retriever(
            search_type="similarity",
            search_kwargs={"k": 3}
        )
        return vectordb_retriver, vector_db

    
    def _create_chain(self):
        """Create the RAG chain"""
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", system_prompt),
            ("human", "{input}")
        ])
        
        qa_chain = create_stuff_documents_chain(self.llm, self.prompt)
        self.rag_chain = create_retrieval_chain(self.retriever, qa_chain)
        logging.info("Retrivel Chain Completed")

    def _shorten_answer(self, text: str, max_words: int = 50) -> str:
        words = text.split()
        if len(words) > max_words:
            return " ".join(words[:max_words]) + "..."
        return text

    
    def cosin_similarity(self, input_text: str) -> str:
        """Get documents using cosine similarity from the retriever"""
        try:
            with self.lock:  # Thread safety if used across threads
                response = self.retriever.invoke(input_text)
                response ="\n\n".join([doc.page_content for doc in response])
                response=self._shorten_answer(response,150)
                logging.info(f"Cosine Similarity Output: {str(response)}")

                return str(response)

        except Exception as e:
            logging.error(f"Cosine Similarity Error: {str(e)}")
            raise CustomException(e, sys)
    
    @measure_time
    def invoke(self, input_text: str) -> str:
        """Process a single query"""
        try:
            with self.lock:  # Thread safety
                logging.info(f"Input Data To RAG: {input_text}")
                response = self.rag_chain.invoke({"input":input_text})
                logging.info(f"Response from Rag: {response}")

                logging.info(f" RAG Retrival Output: {str(response['answer'])}")

                return str(response['answer'])
            
        except Exception as e:
             logging.error(f"Error: {str(e)}")
             raise CustomException(e,sys)

  
    @measure_time
    def search(self, state: GraphState):
        """High-performance document retrieval with caching"""

      
        question = state['question']
        print(question)
        logging.info(f"=========================== RetrieverTool Invoke ===========================")
        print("=" * 20, ' Retriever Tool Invoked', "=" * 20)
        try:

            logging.info(f"🔍 Retrieving documents for: '{question}...'")

            response = self.cosin_similarity(question)
            print(response)

            logging.info(f"Rag Tool Response: {response}")

            web_serach = False
            # if response is None or "i don't know" in response.lower():
            if len(response)<=10:
                web_serach = True    
           
            
            print("=" * 20, ' Retriever Tool Invoked Completed', "=" * 20)
            logging.info(f"=========================== RetrieverTool Invoke Completed ===========================")
            return {"documents": response, "question": question, "use_web_search": web_serach}

        except Exception as e:
            logging.error(f"Vectore Scarch Error {e}")
            raise CustomException(e,sys)
            
        
                