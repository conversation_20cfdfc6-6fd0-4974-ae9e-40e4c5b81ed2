import os
import glob
import logging
from multiprocessing.dummy import Pool as ThreadPool
from tqdm import tqdm
from typing import List
from langchain.docstore.document import Document
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.document_loaders.csv_loader import CSVLoader
from langchain_community.document_loaders import (
    EverNoteLoader,
    PyMuPDFLoader,
    TextLoader,
    UnstructuredEmailLoader,
    UnstructuredEPubLoader,
    UnstructuredHTMLLoader,
    UnstructuredMarkdownLoader,
    UnstructuredODTLoader,
    UnstructuredPowerPointLoader,
    UnstructuredWordDocumentLoader,
)

# Configure logging
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

# Define chunk parameters
chunk_size = 1000
chunk_overlap = 200

# Supported file loaders
LOADER_MAPPING = {
    ".csv": CSVLoader,
    ".doc": UnstructuredWordDocumentLoader,
    ".docx": UnstructuredWordDocumentLoader,
    ".enex": EverNote<PERSON>oader,
    ".epub": UnstructuredEPubLoader,
    ".html": UnstructuredHTMLLoader,
    ".md": UnstructuredMarkdownLoader,
    ".odt": UnstructuredODTLoader,
    ".pdf": PyMuPDFLoader,
    ".ppt": UnstructuredPowerPointLoader,
    ".pptx": UnstructuredPowerPointLoader,
    ".txt": TextLoader,
}

def load_single_document(file_path: str) -> List[Document]:
    """Loads a single document based on its file extension."""
    ext = os.path.splitext(file_path)[-1].lower()
    loader_class = LOADER_MAPPING.get(ext)
    
    if loader_class is None:
        raise ValueError(f"Unsupported file extension '{ext}'")

    loader = loader_class(file_path) if ext != ".txt" else loader_class(file_path, encoding="utf8")
    
    try:
        return loader.load()
    except Exception as e:
        logger.error(f"Failed to load {file_path}: {e}")
        return []

def load_documents(source_dir: str, ignored_files: List[str] = []) -> List[Document]:
    """Loads all documents from a directory, ignoring specified files."""
    all_files = [os.path.join(root, f) for root, _, files in os.walk(source_dir) for f in files]
    filtered_files = [f for f in all_files if os.path.splitext(f)[-1].lower() in LOADER_MAPPING and f not in ignored_files]

    logger.info(f"Found {len(filtered_files)} files to process.")

    with ThreadPool(processes=os.cpu_count()) as pool:
        results = []
        with tqdm(total=len(filtered_files), desc='Loading documents', ncols=80) as pbar:
            for docs in pool.imap_unordered(load_single_document, filtered_files):
                results.extend(docs)
                pbar.update()
    
    return results

def process_documents(source_directory: str, ignored_files: List[str] = []) -> List[Document]:
    """Loads, splits, and processes documents."""
    logger.info(f"Processing documents from {source_directory}")
    
    documents = load_documents(source_directory, ignored_files)
    if not documents:
        logger.warning("No documents were loaded.")
        return []

    text_splitter = RecursiveCharacterTextSplitter(chunk_size=chunk_size, chunk_overlap=chunk_overlap)
    texts = text_splitter.split_documents(documents)

    logger.info(f"Split into {len(texts)} text chunks (max. {chunk_size} tokens each)")
    return texts