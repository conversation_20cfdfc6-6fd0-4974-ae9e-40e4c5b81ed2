import os
import sys
from pathlib import Path
from typing import  Optional, List
from langchain.schema import Document
from langchain_community.document_loaders import(
    EverNoteLoader,
    PyMuPDFLoader,
    TextLoader,
    UnstructuredEmailLoader,
    UnstructuredEPubLoader,
    UnstructuredHTMLLoader,
    UnstructuredMarkdownLoader,
    UnstructuredODTLoader,
    UnstructuredPowerPointLoader,
    UnstructuredWordDocumentLoader,
    CSVLoader
)
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_core.embeddings import Embeddings
from langchain_core.documents import Document
from langchain_community.vectorstores import FAISS

from src.utils.logger import logging
from src.utils.exception import CustomException
from src.utils.main_utils import read_yaml, preprocess_text, extract_keywords, save_keywords
from src.constants import config_file_path

# Supported file loaders
LOADER_MAPPING = {
    ".csv": CSVLoader,
    ".doc": UnstructuredWordDocumentLoader,
    ".docx": UnstructuredWordDocumentLoader,
    ".enex": EverNoteLoader,
    ".epub": UnstructuredEPubLoader,
    ".html": UnstructuredHTMLLoader,
    ".md": UnstructuredMarkdownLoader,
    ".odt": UnstructuredODTLoader,
    ".pdf": PyMuPDFLoader,
    ".ppt": UnstructuredPowerPointLoader,
    ".pptx": UnstructuredPowerPointLoader,
    ".txt": TextLoader,
}

class VectoreDatabase:
    def __init__(self):
        configuration = read_yaml(config_file_path)
        self.config = configuration['RAG']
        self.data = self.config['data_path']
        self.chunking_size = self.config['Chunk_size']
        self.chunking_overlap =self.config['Chunk_overlap']
        self.embeddings = self.config['embedding_model']
        self.model_seq_len = self.config['model_seq_len']
        self.vectore_store_path = self.config['vectore_db_path']
        self.keywords = self.config['keywords']

        self.embeddings = self._load_embddings()

    def data_chunking(self, data_path: Path) -> Optional[List[Document]]:
        all_documents = []

        dir_path = Path(data_path)
        all_documents = []

        for file in dir_path.iterdir():
                if file.suffix == '.pdf':
                    loader = PyPDFLoader(str(file))
            
                else:
                    print(f"Skipping unsupported file type: {file.name}")
                    continue

                # Load the document
                docs = loader.load()

                # Add metadata and wrap in Document schema
                for doc in docs:
                    src = doc.metadata.get("source", str(file.name))
                    all_documents.append(
                        Document(
                            page_content=doc.page_content,
                            metadata={'source': src}
                        )
                    )

        if not all_documents:
                print(f"No valid documents found in '{data_path}'")
                return None

        # Split documents into chunks
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.chunking_size,
            chunk_overlap=self.chunking_overlap,
            separators=["\n\n", "\n", ". ", " ", ""],
            length_function=len
        )
        doc_chunks = text_splitter.split_documents(all_documents)
        print(doc_chunks)

        logging.info(f"Loaded and chunked documents from '{data_path}': {len(doc_chunks)} chunks.")
        print(f"Loaded and chunked documents from '{data_path}': {len(doc_chunks)} chunks.")
        
        # Save keywords 
        docs =[chunk.page_content for chunk in doc_chunks]
        d =[list(map(extract_keywords,docs))[i] for i in range(len(docs))]
        empty=[]
        for i in range(len(d)):
            empty.extend(d[i].split()) 
        uniqe_keywords =set(empty)
        save_keywords(keywords=uniqe_keywords,filepath=self.keywords)

        return doc_chunks

    def _load_embddings(self):
        model_kwargs = {'device': 'cpu'}
        encode_kwargs = {'normalize_embeddings': False}
        embeddings = HuggingFaceEmbeddings(
            model_name=self.embeddings,
            model_kwargs=model_kwargs,
            encode_kwargs=encode_kwargs
        )
        # Embedding dimension
        dim = len(embeddings.embed_query("hello"))
        print(f"dimension of embedding: {dim}")
        logging.info("Model Embedding Load completed")
        return embeddings
    
    def vectore_store(self,embeddins: Embeddings, chunk: List):
        # storing data to vectore db
        vectoredb= FAISS.from_documents(
            documents=chunk,embedding=embeddins
        )
        
        # save in local
        vectoredb.save_local(self.vectore_store_path)
        logging.info(f'vectore data base store path :{self.vectore_store_path}')
       

    def initiate_vectore_store(self):
        try:
            # load_data and chunking
            chunks = self.data_chunking(data_path=self.data)
            logging.info("chunking completed")

            self.vectore_store(self.embeddings,chunks)

            logging.info("Knowledge Completed")
       
        except Exception as e:
            logging.error(f"Error in Vectore store: {e}")
            raise CustomException(e,sys)


    

        