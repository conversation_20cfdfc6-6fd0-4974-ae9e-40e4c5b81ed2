import sys
from typing import  Dict, Any
from functools import lru_cache
from langchain_core.output_parsers import StrOutputParser
from langchain.prompts import ChatPromptTemplate

from src.constants.agent_constants import GraphState
from src.utils.exception import CustomException
from src.utils.logger import logging
from src.prompt import GENERATION_PROMPT, SUPPORTED_TONES
from src.utils.main_utils import measure_time



class GenerationTool:
    def __init__(self,llm) -> None:
        self.llm = llm
       
    
    @measure_time
    def generate(self, state: GraphState) -> Dict[str, Any]:

        logging.info(f"=========================== GenerationTool Invoke ===========================")
        print("=" * 20, 'Generation Tool Invoked', "=" * 20)
        question = state['question']
        document = state['documents']
        print(f"Document: {document}")
        tone = state['tone']

        try:
            # Prompt Modify
            tone = tone.lower() if tone else "professional"
            tone_guide = SUPPORTED_TONES.get(tone, SUPPORTED_TONES["professional"])
            

            # Use pre-compiled prompt for speed
            prompt_template = ChatPromptTemplate.from_template(GENERATION_PROMPT)
            
            generation_chain = prompt_template | self.llm | StrOutputParser()

            output = generation_chain.invoke({"question": question,"document":document, 'tone': tone_guide})

            logging.info(f"Generation Output :\n\t {output}")
            logging.info(f"=========================== GenerationTool Invoke Completed ===========================")
            return {"generation": output,"question": question}
        
        except Exception as e:
            logging.error(f"Exception in Generate :{e}")
            raise CustomException(e,sys)

  
