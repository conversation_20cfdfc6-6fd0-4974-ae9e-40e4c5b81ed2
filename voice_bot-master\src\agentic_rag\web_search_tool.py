import os
import sys
from datetime import datetime
from dotenv import load_dotenv
from typing import List, Dict, Any
from langchain.schema import Document
from langchain_community.utilities import GoogleSerperAPIWrapper

from src.constants.agent_constants import GraphState
from src.utils.exception import CustomException
from src.utils.logger import logging
from src.utils.main_utils import measure_time

load_dotenv()

class GoogleSearchTool:
    def __init__(self) -> None:
        self.search_client = GoogleSerperAPIWrapper(
            hl='en',
            k=5,
            serper_api_key=os.getenv('GOOGLE_SEARCH_API')
        )

    def search(self, query: str) -> List[Document]:
        try:
            # Optional: add date filtering here if needed
            results = self.search_client.results(query)

            documents = []
            for item in results.get("organic", []):
                snippet = item.get("snippet", "")

                # Keep only non-empty snippets
                if len(snippet.strip()) > 10:
                    documents.append(snippet)

            logging.info(f"Search returned {len(documents)} documents")
            return ",".join(documents)

        except Exception as e:
            logging.error(f"Search error: {e}")
            raise CustomException(e, sys)

    @measure_time
    def web_search(self, state: GraphState) -> Dict[str, Any]:
        logging.info("======================= Web search tool invoked =======================")
        question = state['question']

        try:
            response= self.search(question)
            print(response)
            documents = response
            
            return {"documents": documents, "question": question}

        except Exception as e:
            logging.error(f"Web search failed: {e}")
            raise CustomException(e, sys)
