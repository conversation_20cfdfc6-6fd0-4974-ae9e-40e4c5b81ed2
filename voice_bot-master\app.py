from fastapi import <PERSON><PERSON><PERSON>, Response, Request, HTTPException, Query, File, UploadFile
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import uvicorn
from livekit import JobContext
import asyncio
from main import entrypoint
from src.constants.model_request_config import ClientModelConfig,client_sst_config,client_llm_config,client_tts_config
from src.utils.main_utils import save_json
from src.utils.logger import logging
from src.voice_agent.voice_agent import entrypoint
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(title="Voice Agent", version="1.0.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"], 
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)




# Request schema
class ModelRequest(BaseModel):
    model_name: str
    api_key: str




@app.get("/admin/stt")
def sst(model_name: str = Query(..., description="STT model name"), 
        api_key: str = Query(..., description="API key for the model")):

        config = {
            "model_used": model_name,
            "api_key": api_key,
        }

        # Save config globally (could also use session storage/db)
        ClientModelConfig.stt = model_name
        ClientModelConfig.api_key = api_key

        save_json(output_path=client_sst_config, data=config)
        
        logging.info(f"STT model configured: {model_name}")
        return JSONResponse(status_code=200, content={"status": "ok"})
    
@app.get("/api/process-llm")
def llm(model_name: str = Query(..., description="STT model name"), 
        api_key: str = Query(..., description="API key for the model")):

        config = {
            "model_used": model_name,
            "api_key": api_key,
        }

        # Save config globally (could also use session storage/db)
        ClientModelConfig.stt = model_name
        ClientModelConfig.api_key = api_key

        save_json(output_path=client_llm_config, data=config)
        
        logging.info(f"STT model configured: {model_name}")
        return JSONResponse(status_code=200, content={"status": "ok"})

@app.get("/admin/tts")
def tts(model_name: str = Query(..., description="STT model name"), 
        api_key: str = Query(..., description="API key for the model")):

        config = {
            "model_used": model_name,
            "api_key": api_key,
        }

        # Save config globally (could also use session storage/db)
        ClientModelConfig.stt = model_name
        ClientModelConfig.api_key = api_key

        save_json(output_path=client_tts_config, data=config)
        
        logging.info(f"STT model configured: {model_name}")
        return JSONResponse(status_code=200, content={"status": "ok"})

logging.basicConfig(level=logging.INFO)

class VoiceAgentRequest(BaseModel):
    room_url: str
    token: str

class VoiceAgentResponse(BaseModel):
    status: str
    message: str

@app.post("/voice-agent", response_model=VoiceAgentResponse)
async def start_voice_agent(request: VoiceAgentRequest):
    """
    Endpoint to start the voice agent
    """
    try:
        logging.info(f"Starting voice agent for room: {request.room_url}")
        
        # Create JobContext (adjust based on your LiveKit setup)
        ctx = JobContext(room_url=request.room_url, token=request.token)
        
        # Run the voice agent entrypoint
        await entrypoint(ctx)
        
        return VoiceAgentResponse(
            status="success",
            message="Voice agent started successfully"
        )
        
    except Exception as e:
        logging.error(f"Failed to start voice agent: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start voice agent: {str(e)}"
        )


if __name__ == "__main__":
    uvicorn.run(app=app, host="0.0.0.0", port=8000)