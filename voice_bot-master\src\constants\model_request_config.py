from src.utils.main_utils import load_json
class ClientModelConfig:
    stt = None
    api_key = None
    base_url = None
    language = "en"


client_sst_config = r'client_request\stt_config.json'
client_llm_config = r'client_request\llm_config.json'
client_tts_config = r'client_request\tts_config.json'

sst_config = load_json(client_sst_config)
sst_model =sst_config['model_used']
sst_api =sst_config['api_key']


llm_config = load_json(client_llm_config)
llm_model =llm_config['model_used']
llm_api =llm_config['api_key']

tts_config = load_json(client_tts_config)
tts_model =tts_config['model_used']
tts_api =tts_config['api_key']