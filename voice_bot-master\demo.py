import sys
import os
from datetime import datetime
from livekit.agents import Agent, AgentSession, JobContext, llm, cli, WorkerOptions
from livekit.plugins import silero, groq, elevenlabs, deepgram
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import your custom modules with error handling
try:
    from src.utils.logger import logging
    from src.utils.exception import CustomException
    from src.pipeline.agentic_rag_pipeline import AgenticRAG
    print("✅ Successfully imported custom modules")
except ImportError as e:
    print(f"❌ Import error: {e}")
    # Use basic logging as fallback
    import logging
    logging.basicConfig(level=logging.INFO)

    # Create a mock Tool class for testing
    class Tool:
        def run(self, input_data):
            return {"generation": f"Mock response for: {input_data.get('user_transcript', 'No query')}"}

    class CustomException(Exception):
        pass

# Model configuration - with fallbacks
STT_MODEL = os.getenv("STT_MODEL", "nova-2")
LLM_MODEL = os.getenv("LLM_MODEL", "llama3-70b-8192")
TTS_MODEL = os.getenv("TTS_MODEL", "eleven_multilingual_v2")

class LanguageSwitcherAgent(Agent):
    def __init__(self) -> None:
        # Initialize with default language settings
        self.current_language = "en"

        self.language_names = {
            "en": "English",
            "es": "Spanish",
            "fr": "French",
            "de": "German",
            "it": "Italian"
        }

        # ElevenLabs language codes (ISO 639-1 format)
        self.elevenlabs_language_codes = {
            "en": "en",
            "es": "es",
            "fr": "fr",
            "de": "de",
            "it": "it"
        }

        # Deepgram language codes
        self.deepgram_language_codes = {
            "en": "en",
            "es": "es",
            "fr": "fr-CA",
            "de": "de",
            "it": "it"
        }

        self.greetings = {
            "en": "Hello! I'm now speaking in English. How can I help you today?",
            "es": "¡Hola! Ahora estoy hablando en español. ¿Cómo puedo ayudarte hoy?",
            "fr": "Bonjour! Je parle maintenant en français. Comment puis-je vous aider aujourd'hui?",
            "de": "Hallo! Ich spreche jetzt Deutsch. Wie kann ich Ihnen heute helfen?",
            "it": "Ciao! Ora sto parlando in italiano. Come posso aiutarti oggi?"
        }

        super().__init__(
            instructions="""
                You are a helpful assistant communicating through voice.
                You can switch to different languages when asked.
                Available languages: English, Spanish, French, German, and Italian.
                When users ask to switch languages, use the appropriate language switching function.
                Don't use any unpronounceable characters.
            """,
            stt=deepgram.STT(
                model=STT_MODEL,
                language=self.deepgram_language_codes[self.current_language],
                api_key=os.getenv("DEEPGRAM_API_KEY")
            ),
            llm=groq.LLM(
                model=LLM_MODEL,
                api_key=os.getenv("GROQ_API_KEY")
            ),
            tts=elevenlabs.TTS(
                model=TTS_MODEL,
                voice_id="EXAVITQu4vr4xnSDxMaL",  # Default ElevenLabs voice
                language=self.elevenlabs_language_codes[self.current_language],
                api_key="***************************************************"
            ),
            vad=silero.VAD.load()
        )

    async def on_enter(self):
        await self.session.say("Hi there! I can speak in multiple languages including Spanish, French, German, and Italian. Just ask me to switch to any of these languages. How can I help you today?")

    async def _switch_language(self, language_code: str) -> None:
        """Helper method to switch the language"""
        if language_code == self.current_language:
            await self.session.say(f"I'm already speaking in {self.language_names[language_code]}.")
            return

        # Update TTS language
        if self.tts is not None:
            self.tts.update_options(language=self.elevenlabs_language_codes[language_code])

        # Update STT language
        if self.stt is not None:
            deepgram_language = self.deepgram_language_codes.get(language_code, language_code)
            self.stt.update_options(language=deepgram_language)

        self.current_language = language_code

        await self.session.say(self.greetings[language_code])

    @llm.function_tool
    async def switch_to_english(self):
        """Switch to speaking English"""
        await self._switch_language("en")

    @llm.function_tool
    async def switch_to_spanish(self):
        """Switch to speaking Spanish"""
        await self._switch_language("es")

    @llm.function_tool
    async def switch_to_french(self):
        """Switch to speaking French"""
        await self._switch_language("fr")

    @llm.function_tool
    async def switch_to_german(self):
        """Switch to speaking German"""
        await self._switch_language("de")

    @llm.function_tool
    async def switch_to_italian(self):
        """Switch to speaking Italian"""
        await self._switch_language("it")

# Initialize tool agent with error handling
try:
    tool_agent = AgenticRAG()
    print("✅ Tool agent initialized successfully")
except Exception as e:
    print(f"❌ Failed to initialize Tool agent: {e}")
    # Create a mock agent for testing
    class MockTool:
        def run(self, input_data):
            return {"generation": f"Mock response for: {input_data.get('user_transcript', 'No query')}"}
    tool_agent = MockTool()

def create_tool_function():
    """
    Create a custom LangChain tool that wraps the Tool workflow instance.
    """
    @llm.function_tool(
        name="information_tool",
        description="Tool for retrieving factual information and answering questions"
    )
    async def information_tool(query: str) -> str:
        """
        Tool for information retrieval and research.

        Args:
            query: The user's question that needs information

        Returns:
            Response with relevant information
        """
        try:
            logging.info(f"Information tool invoked with query: '{query}'")

            # Prepare input for the tool
            input_data = {"user_transcript": query}
            print(input_data)

            # Invoke the tool workflow
            result = tool_agent.run(query)

            # Extract response
            if isinstance(result, dict) and 'generation' in result:
                response = result['generation']
            else:
                response = str(result)

            logging.info(f"Tool response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error processing query: {str(e)}"
            logging.error(error_msg)
            return error_msg

    return information_tool

async def entrypoint(ctx: JobContext):
    """Main entrypoint for the LiveKit agent with language switching"""
    try:
        print("🚀 Starting LiveKit agent with language switching...")
        logging.info("Starting LiveKit agent with language switching...")

        # Connect to the room
        await ctx.connect()
        print("✅ Connected to room")

        # Create the information tool
        info_tool = create_tool_function()
        print("✅ Information tool created")

        # Create the language switcher agent with tools
        agent = LanguageSwitcherAgent()

        # Add the information tool to the agent
        agent.tools = [info_tool]

        print("✅ Language switcher agent created with tools")

        # Create session
        session = AgentSession()

        # Set up transcription logging
        @session.on("user_input_transcribed")
        def on_transcript(transcript):
            if transcript.is_final:
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                try:
                    with open("user_speech_log.txt", "a", encoding='utf-8') as f:
                        f.write(f"[{timestamp}] {transcript.transcript}\n")
                except Exception as e:
                    print(f"❌ Logging error: {e}")

        print("✅ Transcription logging set up")

        # Start the session
        await session.start(agent=agent, room=ctx.room)
        print("✅ Session started")

        print("🎙️ Language switching agent is now active and listening...")

    except Exception as e:
        error_msg = f"Voice agent error: {e}"
        print(f"❌ {error_msg}")
        logging.error(error_msg)
        raise CustomException(e, sys)

if __name__ == "__main__":
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint, agent_name="language_switcher_agent"))
