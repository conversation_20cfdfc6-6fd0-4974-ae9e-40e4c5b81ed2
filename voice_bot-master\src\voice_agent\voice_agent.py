import sys
from datetime import datetime
from livekit.plugins.turn_detector.multilingual import MultilingualModel
from livekit.agents.voice import Agent, AgentSession
from livekit.agents import Agent, AgentSession, JobContext, llm
from livekit.plugins import silero, groq, cartesia,deepgram
from src.utils.logger import logging
from src.utils.exception import CustomException
from src.pipeline.agentic_rag_pipeline import AgenticRAG
from src.prompt.voice_agent_prompt import instructions
from src.constants.model_request_config import *

rag_agent = AgenticRAG()


def AgentivRAGTool():
    """
    Create a custom LangChain tool that wraps the AgenticRAG workflow instance.
    This tool allows the LLM to intelligently invoke the RAG system when needed.
    """
    @llm.function_tool(name="agentic_rag_tool", description="Essential tool for retrieving factual information, technical data, and current events")
    async def agentic_rag_tool(query: str) -> str:
            """
            MANDATORY TOOL for information retrieval and research. Use this tool for ANY question that requires factual information, current data, or technical knowledge.
            Args:
                query: The user's question that needs factual information or research

            Returns:
                Accurate, comprehensive response with proper source routing
            """
            try:
                logging.info(f"AgenticRAG tool invoked with query: '{query}'")

                # Prepare the input in the format expected by AgenticRAG.run()
                # The run method expects a dictionary with "user_transcript" key
                input_data = {"user_transcript": query}

                # Invoke the AgenticRAG workflow
                result = rag_agent.run(input_data)

                # Extract the generated response from the result
                # The workflow returns a dictionary with various keys, we want the 'generation' key
                if isinstance(result, dict) and 'generation' in result:
                    response = result['generation']
                else:
                    # Fallback if the structure is different
                    response = str(result)

                logging.info(f"AgenticRAG tool response: {response}")
                return response

            except Exception as e:
                logging.error(f"AgenticRAG tool error: {e}")
                # Return a user-friendly error message
                
    return agentic_rag_tool

 
async def entrypoint(ctx: JobContext):
    try:   

        rag_tool = AgentivRAGTool()
        await ctx.connect()  # Agent joins inbound call room automatically

        # Create the AgenticRAG tool
        rag_tool = AgentivRAGTool()
        logging.info("rag agent start initiate")

        session = AgentSession(
            vad=silero.VAD.load(),
            stt=deepgram.STT(model=sst_model, language="multi"),
            llm=groq.LLM(model=llm_model),
            tts=cartesia.TTS(model=tts_model, voice="f786b574-daa5-4673-aa0c-cbe3e8534c02")
        )
        
        @session.on("user_input_transcribed")
        def on_transcript(transcript):
            if transcript.is_final:
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                with open("user_speech_log.txt", "a") as f:
                    f.write(f"[{timestamp}] {transcript.transcript}\n")

        # Create agent with the RAG tool
        agent = Agent(
            llm=session.llm,
            instructions=instructions,
            tools=[rag_tool]
        )

        await session.start(agent=agent, room=ctx.room)
        # await session.say("Hi there! Is there anything I can help you with?")

        await session.generate_reply(instructions="Hello! How may I assist you today?")

    except Exception as e:
        logging.error(f"voice agent error: {e}")
        raise CustomException(e, sys)

   