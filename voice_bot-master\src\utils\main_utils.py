import yaml
import json
import re
import time
from pathlib import Path
from typing import List, Set, Dict, Any
import functools

from src.utils.logger import logging

def read_yaml(file_path:Path):
    with open(file_path) as y:
        data = yaml.safe_load(y)

    return data

def preprocess_text(text:str):
        text = text.lower()  # Convert to lowercase
        text = re.sub(r"[^a-zA-Z0-9\n]", " ", text)  # Remove special characters
        text = re.sub(r"([a-z])([A-Z])", r"\1 \2", text)  # Add spaces between camelCase words
        text = re.sub(r"(\d+)", r" \1 ", text)  # Add spaces around numbers
        text = re.sub(r"\s+", " ", text).strip()  # Remove extra spaces
        return text

def extract_keywords( documents: str):
        # Remove common stopwords
        stopwords = set([
            "with", "from", "that", "this", "your", "have", "been", "around", "and", "the", "for", "is",
            "are", "has", "was", "will", "at", "by", "in", "on", "to", "of", "a", "an", "what", "how", "why"
        ])
        words = re.findall(r'\b[a-zA-Z]{3,}\b', documents)
        return " ".join([word for word in words if word not in stopwords])


def save_keywords(keywords: Set[str], filepath: str):

        with open(filepath, 'w') as f:
            json.dump(list(keywords), f, indent=2)
        print(f"Keywords saved to {filepath}")

def load_keywords( filepath: str) -> Set[str]:

        with open(filepath, 'r') as f:
            keywords = set(json.load(f))
        print(f"Loaded {len(keywords)} keywords from {filepath}")
        return keywords

def measure_time(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start = time.time()
        logging.info(f"🚀 Starting '{func.__name__}'...")
        
        result = func(*args, **kwargs)
        
        end = time.time()
        duration = end - start
        logging.info(f"✅ Finished '{func.__name__}' in {duration:.4f} seconds.")
        return result
    return wrapper

def contains_chunks(question_chunks: str, vector_chunks: set) -> bool:
    """Check if question contains any vector database word chunks."""
    question_words = set(question_chunks.split())
    return bool(question_words & vector_chunks)

def save_json(data: dict, output_path: str, indent: int = 4):
    """
    Save dictionary as a JSON file.
    """
    path = Path(output_path)
    path.parent.mkdir(parents=True, exist_ok=True)
        
    with open(path, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=indent, ensure_ascii=False)
        
    print(f"[✅] JSON saved to: {path.resolve()}")

def load_json(path: str):
    with open(path, "r") as f:
        return json.load(f)