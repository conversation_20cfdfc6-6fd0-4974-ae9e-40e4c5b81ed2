import sys
from typing import Literal, Dict, Any, Optional
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.pydantic_v1 import BaseModel, Field

from src.prompt import route_prompt
from src.constants.agent_constants import GraphState
from src.utils.logger import logging
from src.utils.main_utils import measure_time


# Question Router
class RouteQuery(BaseModel):
    """Route a user query to the most relevant data source."""
    datasource: Literal["vectorstore", "web_search"] = Field(
        ...,
        description="Route the user query to the vectorstore or websearch. Avalable options are 'vectorstore' or 'web_search'",
    )
    confidence: Optional[float] = Field(
        default=None,
        description="Confidence score for the routing decision (0.0 to 1.0)"
    )


class QuestionRouter:
    def __init__(self, llm):
        self.llm = llm 
        
        self.structured_llm_router = llm.with_structured_output(RouteQuery)
        
        router_prompt = ChatPromptTemplate.from_messages(
			[("system", route_prompt), ("human", "{question}")]
		)
        self.question_router = router_prompt | self.structured_llm_router
        


    @measure_time
    def route_question(self, state: GraphState) -> Dict[str, Any]:
        try:
            print("=" * 20, 'Question Routing', "=" * 20)
            logging.info("=========================== Routing Invoke ===============================")


            question = state["question"]
            print(f"Input: {question}")

            # Use the structured router for better decision making
            routing_decision = self.question_router.invoke({"question": question})
            print(f"Routing decision: {routing_decision}")
            logging.info(f"Routing decision: {routing_decision}")
            datasource = routing_decision.datasource

            print(f"Final routing decision: {datasource}")

            if datasource == "web_search":
                print("Routing to web search")
                state = {**state, "use_web_search": True}
            else:
                print("Routing to vectorstore")
                state = {**state, "use_web_search": False}

            print(" ================= routing completed =================")
            logging.info(f"Routing completed: {datasource}")
            logging.info("=========================== Routing Completed ===============================")
            return state

        except Exception as e:
            logging.error(f"Error Route: {e}")
            return  {**state, "use_web_search": True}
      
            